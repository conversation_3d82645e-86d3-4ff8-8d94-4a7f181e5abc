import { supabase } from '@/lib/supabaseClient';
import { NextRequest, NextResponse }
from 'next/server';
import { cookies } from 'next/headers'; // For server-side session access

/**
 * Checks if the current user is an administrator.
 * This function is intended for use in Next.js API routes or Server Components.
 * 
 * It attempts to get the server-side session and then queries the 'profiles'
 * table (or 'users' table, adjust as needed) to check for an 'admin' role.
 * 
 * @returns Promise<boolean> - True if the user is an admin, false otherwise.
 */
export const checkAdminAuth = async (): Promise<boolean> => {
  try {
    const cookieStore = cookies();
    const serverSupabase = supabase; // In this context, supabase client might be pre-configured for server use.
                                   // If using @supabase/ssr, you'd create a server client here.

    const { data: { session }, error: sessionError } = await serverSupabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session for admin check:', sessionError.message);
      return false;
    }

    if (!session?.user) {
      console.log('No active session for admin check.');
      return false;
    }

    // Query the profiles table (or users table) for the user's role
    // Adjust 'profiles' and 'role' column name if different in your schema
    const { data: profile, error: profileError } = await serverSupabase
      .from('profiles') // ASSUMPTION: You have a 'profiles' table with 'id' (matches auth.users.id) and 'role' columns
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile for admin check:', profileError.message);
      // If profile not found, they are not an admin
      if (profileError.code === 'PGRST116') { // PostgREST error for "Searched for one row, but found 0"
        console.log(`Profile not found for user ${session.user.id}`);
        return false;
      }
      return false; 
    }

    return profile?.role === 'admin';

  } catch (error) {
    console.error('Unexpected error during admin check:', error);
    return false;
  }
};

/**
 * Middleware-like function to protect an API route handler with admin authentication.
 * 
 * @param handler The API route handler function to protect.
 * @returns A new handler function that first performs an admin check.
 */
export function withAdminAuth<T extends (request: NextRequest, params?: any) => Promise<NextResponse>>(
  handler: T
) {
  return async (request: NextRequest, params?: any): Promise<NextResponse> => {
    const isAdmin = await checkAdminAuth();
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized: Admin access required' }, { status: 403 });
    }
    return handler(request, params);
  };
}